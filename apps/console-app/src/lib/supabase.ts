import { create<PERSON>rowser<PERSON><PERSON>, createServer<PERSON>lient, isBrowser } from '@supabase/ssr'
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public'
import type { Database } from '../../../../packages/database-types'

// Browser client for client-side operations
export const supabase = createBrowserClient<Database>(
  PUBLIC_SUPABASE_URL,
  PUBLIC_SUPABASE_ANON_KEY
)

// Server client factory for server-side operations
export function createSupabaseServerClient(cookies: {
  get: (name: string) => string | undefined
  set: (name: string, value: string, options?: any) => void
  remove: (name: string, options?: any) => void
}) {
  return createServerClient<Database>(
    PUBLIC_SUPABASE_URL,
    PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return [
            { name: 'sb-access-token', value: cookies.get('sb-access-token') || '' },
            { name: 'sb-refresh-token', value: cookies.get('sb-refresh-token') || '' }
          ].filter(cookie => cookie.value)
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookies.set(name, value, { ...options, path: '/' })
          })
        }
      }
    }
  )
}

