{"name": "psii-auth-test", "version": "1.0.0", "type": "module", "description": "Authentication test script for PSII", "scripts": {"smart-setup": "node scripts/smart-setup.js", "setup-ports": "node scripts/setup-ports.js", "docker-setup": "node scripts/docker-setup.js", "test-auth": "node scripts/auth-test.js", "test-e2e": "playwright test", "test-e2e-ui": "playwright test --ui", "test-complete": "node scripts/test-complete.js", "create-test-users": "node scripts/create-test-users.js", "check-db": "node scripts/check-db.js", "fix-user-ids": "node scripts/fix-user-ids.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "node-fetch": "^3.3.2", "playwright": "^1.53.0"}, "devDependencies": {"@playwright/test": "^1.53.0"}}